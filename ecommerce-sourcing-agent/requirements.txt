# E-commerce Product Sourcing Agent Requirements

# Core Strands framework
strands>=0.1.0
strands-tools>=0.1.0

# AWS SDK for Bedrock integration
boto3>=1.34.0
botocore>=1.34.0

# Data processing and analysis
pandas>=2.0.0
numpy>=1.24.0

# HTTP requests for API integrations (when implementing real APIs)
requests>=2.31.0
aiohttp>=3.8.0

# SerpAPI for Amazon search integration
google-search-results>=2.4.2

# Environment variable management
python-dotenv>=1.0.0

# JSON and data validation
pydantic>=2.0.0
jsonschema>=4.17.0

# Logging and monitoring
structlog>=23.1.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.7.0
flake8>=6.0.0

# Jupyter notebook support
jupyter>=1.0.0
ipykernel>=6.25.0

# Optional: For enhanced data visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0
